<template>
  <div class="settings-container">
    <div class="settings-header">
      <h2>设置</h2>
      <p>配置应用参数和数据同步</p>
    </div>

    <div class="settings-content">
      <!-- 飞书配置区域 -->
      <div class="settings-section">
        <h3>飞书多维表格配置</h3>
        <div class="form-group">
          <label>App Token:</label>
          <t-input
            v-model="feishuConfig.appToken"
            placeholder="默认：RR9cb8uqKaBUOGso9WTcBiXjnfh"
            class="config-input"
          />
          <div class="help-text">
            在飞书多维表格中，点击右上角"..."→"高级"→"开发者选项"获取<br>
            或从URL中提取：https://xxx.feishu.cn/base/<strong>RR9cb8uqKaBUOGso9WTcBiXjnfh</strong>/tbl.../viw...<br>
            <strong>注意：只需要App Token部分，不包含"base/"前缀</strong>
          </div>
        </div>

        <div class="form-group">
          <label>热门秋招汇总 Table ID:</label>
          <t-input
            v-model="feishuConfig.tableId"
            placeholder="默认：tbl1arQX7A2jiLh4"
            class="config-input"
          />
          <div class="help-text">
            热门秋招汇总表的Table ID
          </div>
        </div>

        <div class="form-group">
          <label>热门实习汇总 Table ID:</label>
          <t-input
            v-model="feishuConfig.internshipTableId"
            placeholder="默认：tbl2tPFOPenoIQdg"
            class="config-input"
          />
          <div class="help-text">
            热门实习汇总表的Table ID
          </div>
        </div>

        <div class="form-group">
          <label>国企央企汇总 Table ID:</label>
          <t-input
            v-model="feishuConfig.stateOwnedTableId"
            placeholder="默认：tbl1UfSuMtFZ8GR9"
            class="config-input"
          />
          <div class="help-text">
            国企央企汇总表的Table ID<br>
            <strong>所有Table ID都以"tbl"开头</strong>
          </div>
        </div>

        <div class="form-group">
          <label>App ID:</label>
          <t-input
            v-model="feishuConfig.appId"
            placeholder="默认：cli_a80ce54e0c78100c"
            class="config-input"
          />
          <div class="help-text">
            在飞书开放平台应用管理页面的"凭证与基础信息"中获取
          </div>
        </div>

        <div class="form-group">
          <label>App Secret:</label>
          <t-input
            v-model="feishuConfig.appSecret"
            type="password"
            placeholder="默认：6l0TOvHiepUuHD5VyxmRwcXHLoRalwfQ"
            class="config-input"
          />
          <div class="help-text">
            在飞书开放平台应用管理页面的"凭证与基础信息"中获取
          </div>
        </div>

        <div class="form-actions">
          <t-button theme="primary" @click="saveConfig">
            保存配置
          </t-button>
          <t-button theme="default" @click="testConnection">
            测试连接
          </t-button>
          <t-button theme="warning" variant="outline" @click="resetToDefault">
            恢复默认配置
          </t-button>
        </div>
      </div>

      <!-- 数据同步区域 -->
      <div class="settings-section">
        <h3>数据同步</h3>
        <div class="sync-info">
          <p>从飞书多维表格同步校招数据到本地校招汇总表</p>
          <div class="sync-status">
            <span>上次同步时间: {{ lastSyncTime || '从未同步' }}</span>
          </div>
        </div>

        <div class="form-actions">
          <t-button
            theme="warning"
            @click="updateHotAutumnData"
            :loading="isUpdatingAutumn"
            :disabled="!isConfigValid"
          >
            <template v-if="isUpdatingAutumn">
              更新中...
            </template>
            <template v-else>
              🔥 更新热门秋招汇总
            </template>
          </t-button>

          <t-button
            theme="primary"
            @click="updateInternshipData"
            :loading="isUpdatingInternship"
            :disabled="!isInternshipConfigValid"
          >
            <template v-if="isUpdatingInternship">
              更新中...
            </template>
            <template v-else>
              💼 更新热门实习汇总
            </template>
          </t-button>

          <t-button
            theme="success"
            @click="updateStateOwnedData"
            :loading="isUpdatingStateOwned"
            :disabled="!isStateOwnedConfigValid"
          >
            <template v-if="isUpdatingStateOwned">
              更新中...
            </template>
            <template v-else>
              🏢 更新国企央企汇总
            </template>
          </t-button>
        </div>
      </div>

      <!-- 数据管理区域 -->
      <div class="settings-section">
        <h3>📊 数据管理</h3>
        <div class="data-status">
          <div class="data-item">
            <span class="data-label">数据库状态:</span>
            <span class="data-count" :class="{ 'status-healthy': databaseStore.initialized, 'status-error': !databaseStore.initialized }">
              {{ databaseStore.initialized ? '✅ 正常' : '❌ 异常' }}
            </span>
            <span class="data-storage">(IndexedDB)</span>
          </div>
          <div class="data-item">
            <span class="data-label">热门秋招汇总:</span>
            <span class="data-count">{{ databaseStore.hotAutumnData.length }} 条记录</span>
            <span class="data-storage">(数据库)</span>
          </div>
          <div class="data-item">
            <span class="data-label">热门实习汇总:</span>
            <span class="data-count">{{ databaseStore.hotInternshipData.length }} 条记录</span>
            <span class="data-storage">(数据库)</span>
          </div>
          <div class="data-item">
            <span class="data-label">国企央企汇总:</span>
            <span class="data-count">{{ databaseStore.stateOwnedData.length }} 条记录</span>
            <span class="data-storage">(数据库)</span>
          </div>
          <div class="data-item">
            <span class="data-label">求职申请:</span>
            <span class="data-count">{{ databaseStore.jobApplications.length }} 条记录</span>
            <span class="data-storage">(数据库)</span>
          </div>
        </div>

        <div class="data-actions">
          <t-button
            theme="danger"
            variant="outline"
            @click="repairDatabase"
            :loading="isRepairingDatabase"
            :disabled="databaseStore.initialized"
          >
            🔧 修复数据库
          </t-button>
          <t-button
            theme="primary"
            variant="outline"
            @click="checkDatabaseHealth"
            :loading="isCheckingDatabase"
          >
            🔍 检查数据库
          </t-button>
          <t-button theme="warning" variant="outline" @click="clearAllData">
            🗑️ 清空所有数据
          </t-button>
          <t-button theme="primary" variant="outline" @click="exportData">
            📤 导出数据
          </t-button>
          <t-button theme="success" variant="outline" @click="refreshDataStatus">
            🔄 刷新状态
          </t-button>
        </div>
      </div>

      <!-- 字段映射区域 -->
      <div class="settings-section">
        <h3>字段映射配置</h3>
        <div class="mapping-info">
          <p>配置飞书多维表格字段与本地字段的对应关系</p>
        </div>
        
        <div class="field-mapping">
          <div class="mapping-item" v-for="(mapping, key) in fieldMappings" :key="key">
            <label>{{ mapping.label }}:</label>
            <t-input
              v-model="mapping.feishuField"
              :placeholder="`飞书表格中的${mapping.label}字段名`"
              class="mapping-input"
            />
          </div>
        </div>

        <div class="form-actions">
          <t-button theme="default" @click="saveFieldMappings">
            保存字段映射
          </t-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  Input as TInput,
  Button as TButton,
  MessagePlugin
} from 'tdesign-vue-next'
import { useRecruitmentStore } from '../stores/recruitmentStore'
import { useDatabaseStore } from '../stores/databaseStore'
import {
  createFeishuClient,
  validateConfig,
  loadConfigFromStorage,
  saveConfigToStorage,
  loadFieldMappingFromStorage,
  saveFieldMappingToStorage,
  convertFeishuRecord,
  type FeishuConfig,
  type FieldMapping
} from '../services/feishuApi'

// Store
const recruitmentStore = useRecruitmentStore()
const databaseStore = useDatabaseStore()

// 默认飞书配置
const defaultFeishuConfig: FeishuConfig = {
  appToken: 'RR9cb8uqKaBUOGso9WTcBiXjnfh',
  tableId: 'tbl1arQX7A2jiLh4',
  internshipTableId: 'tbl2tPFOPenoIQdg',
  stateOwnedTableId: 'tbl1UfSuMtFZ8GR9',
  appId: 'cli_a80ce54e0c78100c',
  appSecret: '6l0TOvHiepUuHD5VyxmRwcXHLoRalwfQ'
}

// 飞书配置
const feishuConfig = ref<FeishuConfig>({
  appToken: '',
  tableId: '',
  internshipTableId: '',
  stateOwnedTableId: '',
  appId: '',
  appSecret: ''
})

// 字段映射配置
const fieldMappings = ref({
  updateTime: { label: '更新', feishuField: '更新时间' },
  company: { label: '公司', feishuField: '公司' },
  applicationLink: { label: '投递链接', feishuField: '投递链接' },
  industry: { label: '行业', feishuField: '行业' },
  tags: { label: '标签', feishuField: '标签' },
  batch: { label: '批次', feishuField: '批次' },
  isHot: { label: '热门', feishuField: '热门' },
  position: { label: '职位', feishuField: '职位' },
  location: { label: '地点', feishuField: '地点' },
  deadline: { label: '投递截至', feishuField: '投递截至' }
})

// 状态
const isUpdatingAutumn = ref(false)
const isUpdatingInternship = ref(false)
const isUpdatingStateOwned = ref(false)
const isRepairingDatabase = ref(false)
const isCheckingDatabase = ref(false)
const lastSyncTime = ref('')

// 计算属性
const isConfigValid = computed(() => {
  return feishuConfig.value.appToken &&
         feishuConfig.value.tableId &&
         feishuConfig.value.appId &&
         feishuConfig.value.appSecret
})

const isInternshipConfigValid = computed(() => {
  return feishuConfig.value.appToken &&
         feishuConfig.value.internshipTableId &&
         feishuConfig.value.appId &&
         feishuConfig.value.appSecret
})

const isStateOwnedConfigValid = computed(() => {
  return feishuConfig.value.appToken &&
         feishuConfig.value.stateOwnedTableId &&
         feishuConfig.value.appId &&
         feishuConfig.value.appSecret
})

// 数据库修复方法
const repairDatabase = async () => {
  if (!confirm('确定要修复数据库吗？这将重新初始化数据库连接。')) {
    return
  }

  isRepairingDatabase.value = true
  try {
    MessagePlugin.info('正在修复数据库...')
    const success = await databaseStore.reinitializeDatabase()

    if (success) {
      MessagePlugin.success('数据库修复成功')
    } else {
      MessagePlugin.error('数据库修复失败，请重启应用')
    }
  } catch (error) {
    console.error('数据库修复失败:', error)
    MessagePlugin.error('数据库修复失败，请重启应用')
  } finally {
    isRepairingDatabase.value = false
  }
}

// 数据库健康检查方法
const checkDatabaseHealth = async () => {
  isCheckingDatabase.value = true
  try {
    MessagePlugin.info('正在检查数据库状态...')
    const isHealthy = await databaseStore.checkDatabaseHealth()

    if (isHealthy) {
      MessagePlugin.success('数据库状态正常')
    } else {
      MessagePlugin.warning('数据库状态异常，建议进行修复')
    }
  } catch (error) {
    console.error('数据库检查失败:', error)
    MessagePlugin.error('数据库检查失败')
  } finally {
    isCheckingDatabase.value = false
  }
}

// 数据管理方法
const clearAllData = async () => {
  if (confirm('确定要清空所有数据吗？此操作不可恢复！')) {
    try {
      await databaseStore.clearAllData()
      // 同时清空原有的localStorage（兼容性）
      localStorage.removeItem('recruitment-data')
      localStorage.removeItem('hot-internship-data')
      localStorage.removeItem('state-owned-data')

      MessagePlugin.success('所有数据已清空')
    } catch (error) {
      MessagePlugin.error('清空数据失败')
    }
  }
}

const exportData = async () => {
  try {
    const allData = await databaseStore.exportAllData()

    const dataStr = JSON.stringify(allData, null, 2)
    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(blob)

    const a = document.createElement('a')
    a.href = url
    a.download = `recruitment-data-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    MessagePlugin.success('数据导出成功')
  } catch (error) {
    MessagePlugin.error('导出数据失败')
  }
}

const refreshDataStatus = async () => {
  try {
    await databaseStore.initializeDatabase()
    MessagePlugin.info('数据状态已刷新')
  } catch (error) {
    MessagePlugin.error('刷新数据状态失败')
  }
}





// 初始化
onMounted(async () => {
  await loadConfig()
  loadLastSyncTime()

  // 确保recruitment store正确初始化
  console.log('初始化时检查store:', recruitmentStore.recruitments)
  if (!recruitmentStore.recruitments || recruitmentStore.recruitments.length === 0) {
    console.log('Store未初始化，手动初始化')
    recruitmentStore.loadFromStorage()
  }

  // 初始化数据库store（在App.vue中已经初始化）
  // databaseStore已在App.vue中初始化
})

// 加载配置
const loadConfig = async () => {
  try {
    // 首先尝试从数据库加载配置
    const dbConfig = await databaseStore.getFeishuConfig()
    if (dbConfig) {
      feishuConfig.value = {
        appToken: dbConfig.appToken,
        tableId: dbConfig.tableId,
        internshipTableId: dbConfig.internshipTableId,
        stateOwnedTableId: dbConfig.stateOwnedTableId,
        appId: dbConfig.appId,
        appSecret: dbConfig.appSecret
      }
      console.log('从数据库加载飞书配置成功')
    } else {
      // 如果数据库没有配置，尝试从localStorage加载
      const savedConfig = loadConfigFromStorage()
      if (savedConfig) {
        feishuConfig.value = savedConfig
        // 同时保存到数据库
        await databaseStore.saveFeishuConfig(savedConfig)
        console.log('从localStorage加载配置并同步到数据库')
      } else {
        // 如果都没有，使用默认配置
        feishuConfig.value = { ...defaultFeishuConfig }
        // 保存默认配置到数据库和localStorage
        await databaseStore.saveFeishuConfig(defaultFeishuConfig)
        saveConfigToStorage(defaultFeishuConfig)
        console.log('使用默认飞书配置')
        MessagePlugin.info('已加载默认飞书配置，您可以根据需要进行修改')
      }
    }

    const savedMappings = loadFieldMappingFromStorage()
    if (savedMappings) {
      // 转换为组件需要的格式
      Object.keys(fieldMappings.value).forEach(key => {
        if (savedMappings[key]) {
          (fieldMappings.value as any)[key].feishuField = savedMappings[key]
        }
      })
    }
  } catch (error) {
    console.error('加载配置失败:', error)
    // 如果加载失败，至少使用默认配置
    feishuConfig.value = { ...defaultFeishuConfig }
    MessagePlugin.warning('配置加载失败，已使用默认配置')
  }
}

// 加载上次同步时间
const loadLastSyncTime = () => {
  const saved = localStorage.getItem('last-sync-time')
  if (saved) {
    lastSyncTime.value = saved
  }
}

// 保存配置
const saveConfig = async () => {
  try {
    if (!validateConfig(feishuConfig.value)) {
      MessagePlugin.warning('请完善所有配置项')
      return
    }

    // 同时保存到数据库和localStorage
    await databaseStore.saveFeishuConfig(feishuConfig.value)
    saveConfigToStorage(feishuConfig.value)
    MessagePlugin.success('配置保存成功')
  } catch (error) {
    console.error('保存配置失败:', error)
    MessagePlugin.error('配置保存失败')
  }
}

// 恢复默认配置
const resetToDefault = async () => {
  if (!confirm('确定要恢复默认配置吗？这将覆盖当前的所有配置。')) {
    return
  }

  try {
    feishuConfig.value = { ...defaultFeishuConfig }
    await databaseStore.saveFeishuConfig(defaultFeishuConfig)
    saveConfigToStorage(defaultFeishuConfig)
    MessagePlugin.success('已恢复默认配置')
  } catch (error) {
    console.error('恢复默认配置失败:', error)
    MessagePlugin.error('恢复默认配置失败')
  }
}

// 保存字段映射
const saveFieldMappings = () => {
  try {
    // 转换为API需要的格式
    const mapping: FieldMapping = {}
    Object.keys(fieldMappings.value).forEach(key => {
      mapping[key] = (fieldMappings.value as any)[key].feishuField
    })

    saveFieldMappingToStorage(mapping)
    MessagePlugin.success('字段映射保存成功')
  } catch (error) {
    console.error('保存字段映射失败:', error)
    MessagePlugin.error('字段映射保存失败')
  }
}

// 测试连接
const testConnection = async () => {
  if (!isConfigValid.value) {
    MessagePlugin.warning('请先完善配置信息')
    return
  }

  try {
    MessagePlugin.info('正在测试连接...')

    const client = createFeishuClient(feishuConfig.value)
    const success = await client.testConnection()

    if (success) {
      MessagePlugin.success('连接测试成功')
    } else {
      MessagePlugin.error('连接测试失败，请检查配置')
    }
  } catch (error) {
    MessagePlugin.error('连接测试失败')
  }
}

// 通用的数据更新方法
const updateDataFromFeishu = async (tableId: string, targetData: any[], dataType: string) => {
  const client = createFeishuClient({
    ...feishuConfig.value,
    tableId: tableId
  })

  // 获取所有记录
  const records = await client.getAllRecords()
  console.log(`获取到的${dataType}原始记录:`, records)

  // 检查records是否有效
  if (!records || !Array.isArray(records)) {
    throw new Error('获取到的数据格式不正确或为空')
  }

  // 准备字段映射
  const mapping: FieldMapping = {}
  Object.keys(fieldMappings.value).forEach(key => {
    mapping[key] = (fieldMappings.value as any)[key].feishuField
  })


  // 转换数据格式
  const convertedRecords = records.map((record, index) =>
    convertFeishuRecord(record, mapping, index)
  )
  console.log(`转换后的${dataType}记录:`, convertedRecords)

  // 更新目标数据
  targetData.length = 0
  targetData.push(...convertedRecords)

  return convertedRecords.length
}

// 更新热门秋招汇总数据
const updateHotAutumnData = async () => {
  if (!isConfigValid.value) {
    MessagePlugin.warning('请先完善热门秋招汇总的配置信息')
    return
  }

  isUpdatingAutumn.value = true

  try {
    MessagePlugin.info('正在从飞书多维表格获取热门秋招汇总数据...')

    const count = await updateDataFromFeishu(
      feishuConfig.value.tableId,
      recruitmentStore.recruitments,
      '热门秋招汇总'
    )

    // 检查数据库健康状态并保存数据
    try {
      await databaseStore.saveHotAutumnData([...recruitmentStore.recruitments])
    } catch (dbError) {
      console.error('数据库保存失败，尝试重新初始化:', dbError)
      MessagePlugin.warning('数据库保存失败，正在尝试修复...')

      const repairSuccess = await databaseStore.reinitializeDatabase()
      if (repairSuccess) {
        await databaseStore.saveHotAutumnData([...recruitmentStore.recruitments])
        MessagePlugin.success('数据库修复成功，数据已保存')
      } else {
        MessagePlugin.error('数据库修复失败，数据仅保存到本地缓存')
      }
    }

    // 保存到store
    recruitmentStore.saveToStorage()

    // 保存同步时间
    const now = new Date().toLocaleString('zh-CN')
    lastSyncTime.value = now
    localStorage.setItem('last-sync-time', now)

    MessagePlugin.success(`热门秋招汇总数据更新成功，共同步 ${count} 条记录`)

  } catch (error) {
    console.error('更新数据失败:', error)
    MessagePlugin.error('热门秋招汇总数据更新失败')
  } finally {
    isUpdatingAutumn.value = false
  }
}

// 更新热门实习汇总数据
const updateInternshipData = async () => {
  if (!isInternshipConfigValid.value) {
    MessagePlugin.warning('请先完善热门实习汇总的配置信息')
    return
  }

  isUpdatingInternship.value = true

  try {
    MessagePlugin.info('正在从飞书多维表格获取热门实习汇总数据...')

    // 创建临时数组来接收数据
    const tempData: any[] = []
    const count = await updateDataFromFeishu(
      feishuConfig.value.internshipTableId!,
      tempData,
      '热门实习汇总'
    )

    // 直接通过数据库store更新数据
    console.log('通过数据库store更新实习数据')
    await databaseStore.saveHotInternshipData(tempData)

    MessagePlugin.success(`热门实习汇总数据更新成功，共同步 ${count} 条记录`)

  } catch (error) {
    MessagePlugin.error('热门实习汇总数据更新失败')
  } finally {
    isUpdatingInternship.value = false
  }
}

// 更新国企央企汇总数据
const updateStateOwnedData = async () => {
  if (!isStateOwnedConfigValid.value) {
    MessagePlugin.warning('请先完善国企央企汇总的配置信息')
    return
  }

  isUpdatingStateOwned.value = true

  try {
    MessagePlugin.info('正在从飞书多维表格获取国企央企汇总数据...')

    // 创建临时数组来接收数据
    const tempData: any[] = []
    const count = await updateDataFromFeishu(
      feishuConfig.value.stateOwnedTableId!,
      tempData,
      '国企央企汇总'
    )

    // 直接通过数据库store更新数据
    console.log('通过数据库store更新国企数据')
    await databaseStore.saveStateOwnedData(tempData)

    MessagePlugin.success(`国企央企汇总数据更新成功，共同步 ${count} 条记录`)

  } catch (error) {
    MessagePlugin.error('国企央企汇总数据更新失败')
  } finally {
    isUpdatingStateOwned.value = false
  }
}


</script>

<style scoped>
.settings-container {
  height: 100%;
  padding: 24px;
  background-color: #fff;
  overflow-y: auto;
}

.settings-header {
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e7e7e7;
}

.settings-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.settings-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.settings-content {
  max-width: 800px;
}

.settings-section {
  margin-bottom: 40px;
  padding: 24px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e7e7e7;
}

.settings-section h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.config-input,
.mapping-input {
  width: 100%;
  margin-bottom: 4px;
}

.help-text {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.sync-info {
  margin-bottom: 20px;
}

.sync-info p {
  margin: 0 0 8px 0;
  color: #666;
}

.sync-status {
  font-size: 12px;
  color: #999;
}

.mapping-info {
  margin-bottom: 20px;
}

.mapping-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.field-mapping {
  display: grid;
  gap: 16px;
}

.mapping-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mapping-item label {
  min-width: 100px;
  margin: 0;
  font-size: 14px;
}

.mapping-input {
  flex: 1;
}

/* 数据管理样式 */
.data-status {
  margin-bottom: 16px;
}

.data-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.data-item:last-child {
  border-bottom: none;
}

.data-label {
  font-weight: 500;
  min-width: 120px;
  color: #333;
}

.data-count {
  font-weight: bold;
  color: #1890ff;
  margin-right: 8px;
}

.data-count.status-healthy {
  color: #16a34a;
}

.data-count.status-error {
  color: #dc2626;
}

.data-storage {
  font-size: 12px;
  color: #666;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.data-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.data-actions .t-button {
  flex: 1;
  min-width: 120px;
}
</style>
