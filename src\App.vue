<script setup lang="ts">
import { ref, onMounted } from 'vue'
import JobApplicationTable from './components/JobApplicationTable.vue'
import RecruitmentSummaryTable from './components/RecruitmentSummaryTable.vue'
import SettingsPage from './components/SettingsPage.vue'
import CustomTitleBar from './components/CustomTitleBar.vue'
import { useDatabaseStore } from './stores/databaseStore'

// 当前激活的页面
const currentPage = ref('job-application')

// 切换页面
const switchPage = (page: string) => {
  currentPage.value = page
}

// 使用数据库store
const databaseStore = useDatabaseStore()

// 应用启动时初始化数据库
onMounted(async () => {
  try {
    await databaseStore.initializeDatabase()
  } catch (error) {
    // 静默处理初始化错误
  }
})

// 全局数据更新方法，供设置页面调用
declare global {
  interface Window {
    updateRecruitmentTableData: {
      updateInternshipData: (data: any[]) => void
      updateStateOwnedData: (data: any[]) => void
    }
  }
}

// 初始化全局接口，直接使用数据库store方法
window.updateRecruitmentTableData = {
  updateInternshipData: async (data: any[]) => {
    await databaseStore.saveHotInternshipData(data)
  },
  updateStateOwnedData: async (data: any[]) => {
    await databaseStore.saveStateOwnedData(data)
  }
}
</script>

<template>
  <div id="app">
    <CustomTitleBar />
    <div class="app-content">
      <!-- 左侧菜单导航栏 -->
      <div class="sidebar">
        <div class="sidebar-header">
          <h3>菜单</h3>
        </div>
        <div class="sidebar-menu">
          <div
            class="menu-item"
            :class="{ active: currentPage === 'job-application' }"
            @click="switchPage('job-application')"
          >
            <span class="menu-icon">📋</span>
            <span class="menu-text">求职申请表</span>
          </div>
          <div
            class="menu-item"
            :class="{ active: currentPage === 'recruitment-summary' }"
            @click="switchPage('recruitment-summary')"
          >
            <span class="menu-icon">📊</span>
            <span class="menu-text">校招汇总表</span>
          </div>
          <div
            class="menu-item"
            :class="{ active: currentPage === 'settings' }"
            @click="switchPage('settings')"
          >
            <span class="menu-icon">⚙️</span>
            <span class="menu-text">设置</span>
          </div>
        </div>
      </div>

      <!-- 右侧主页面 -->
      <div class="main-content">
        <JobApplicationTable v-if="currentPage === 'job-application'" />
        <RecruitmentSummaryTable v-else-if="currentPage === 'recruitment-summary'" />
        <SettingsPage v-else-if="currentPage === 'settings'" />
        <div v-else class="placeholder-content">
          <h2>功能开发中...</h2>
          <p>该功能正在开发中，敬请期待。</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
#app {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.app-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  margin-top: 32px; /* 为固定标题栏留出空间 */
  height: calc(100vh - 32px); /* 减去标题栏高度 */
}

/* 左侧菜单导航栏 */
.sidebar {
  width: 100px;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
}

.sidebar-header {
  padding: 20px 8px 16px;
  border-bottom: 1px solid #e2e8f0;
  text-align: center;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.sidebar-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #475569;
  letter-spacing: 0.5px;
}

.sidebar-menu {
  flex: 1;
  padding: 12px 0;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  margin: 6px 8px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.menu-item:hover {
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.menu-item:hover::before {
  opacity: 1;
}

.menu-item.active {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(79, 70, 229, 0.3);
}

.menu-item.active::before {
  opacity: 1;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.2) 100%);
}

.menu-icon {
  font-size: 22px;
  margin-bottom: 6px;
  transition: transform 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.menu-item:hover .menu-icon {
  transform: scale(1.1);
}

.menu-item.active .menu-icon {
  transform: scale(1.05);
}

.menu-text {
  font-size: 10px;
  line-height: 1.3;
  word-break: break-all;
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* 右侧主页面 */
.main-content {
  flex: 1;
  overflow: hidden;
  background-color: #fff;
}

/* 占位内容样式 */
.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
}

.placeholder-content h2 {
  margin-bottom: 16px;
  font-size: 24px;
  font-weight: 500;
}

.placeholder-content p {
  font-size: 16px;
  margin: 0;
}

/* 全局移除所有按钮的焦点边框 */
:deep(.t-button) {
  outline: none !important;
}

:deep(.t-button:focus) {
  outline: none !important;
  box-shadow: none !important;
}

/* 移除菜单项的焦点边框 */
.menu-item:focus {
  outline: none !important;
}
</style>
