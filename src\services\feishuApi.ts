// 飞书多维表格API服务

export interface FeishuConfig {
  appToken: string
  tableId: string
  internshipTableId?: string
  stateOwnedTableId?: string
  appId: string
  appSecret: string
}

export interface FeishuRecord {
  record_id: string
  fields: Record<string, any>
  created_by?: any
  created_time?: number
  last_modified_by?: any
  last_modified_time?: number
}

export interface FeishuApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

export interface ListRecordsResponse {
  has_more: boolean
  page_token?: string
  total: number
  items: FeishuRecord[]
}

export interface TenantAccessTokenResponse {
  code: number
  msg: string
  tenant_access_token: string
  expire: number
}

/**
 * 飞书多维表格API客户端
 */
export class FeishuApiClient {
  private config: FeishuConfig
  private tenantAccessToken: string | null = null
  private tokenExpireTime: number = 0

  constructor(config: FeishuConfig) {
    this.config = config
  }

  /**
   * 获取API基础URL
   */
  private getBaseUrl(): string {
    return 'https://open.feishu.cn/open-apis'
  }

  /**
   * 发送HTTP请求（通过Electron代理）
   */
  private async makeRequest(url: string, options: {
    method: string,
    headers?: Record<string, string>,
    body?: string
  }): Promise<any> {
    // 检查是否在Electron环境中
    if (typeof window !== 'undefined' && window.electronAPI?.feishuApiRequest) {
      return await window.electronAPI.feishuApiRequest({
        url,
        method: options.method,
        headers: options.headers,
        body: options.body
      })
    } else {
      // 降级到普通fetch（开发环境）
      const response = await fetch(url, {
        method: options.method,
        headers: options.headers,
        body: options.body
      })

      const data = await response.json()

      return {
        ok: response.ok,
        status: response.status,
        statusText: response.statusText,
        data: data
      }
    }
  }

  /**
   * 获取tenant_access_token
   */
  private async getTenantAccessToken(): Promise<string> {
    // 检查token是否有效
    const now = Date.now()
    if (this.tenantAccessToken && this.tokenExpireTime > now) {
      return this.tenantAccessToken
    }

    // 获取新的token
    const response = await this.makeRequest('https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        app_id: this.config.appId,
        app_secret: this.config.appSecret
      })
    })

    if (!response.ok) {
      throw new Error(`获取tenant_access_token失败: HTTP ${response.status}`)
    }

    const data: TenantAccessTokenResponse = response.data

    if (data.code !== 0) {
      throw new Error(`获取tenant_access_token失败: ${data.msg}`)
    }

    // 缓存token（提前5分钟过期）
    this.tenantAccessToken = data.tenant_access_token
    this.tokenExpireTime = now + (data.expire - 300) * 1000

    return this.tenantAccessToken
  }

  /**
   * 构建请求头
   */
  private async getHeaders(): Promise<Record<string, string>> {
    const token = await this.getTenantAccessToken()
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }

  /**
   * 发送HTTP请求
   */
  private async request<T = any>(
    endpoint: string,
    options: {
      method?: string,
      headers?: Record<string, string>,
      body?: string
    } = {}
  ): Promise<FeishuApiResponse<T>> {
    const url = `${this.getBaseUrl()}${endpoint}`
    const headers = await this.getHeaders()

    const response = await this.makeRequest(url, {
      method: options.method || 'GET',
      headers: {
        ...headers,
        ...options.headers
      },
      body: options.body
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = response.data

    if (data.code !== 0) {
      throw new Error(data.msg || '请求失败')
    }

    return data
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.listRecords(1) // 只获取1条记录来测试
      return true
    } catch (error) {
      console.error('连接测试失败:', error)
      return false
    }
  }

  /**
   * 列出记录
   * @param pageSize 每页记录数，最大500
   * @param pageToken 分页标记
   */
  async listRecords(
    pageSize: number = 500, 
    pageToken?: string
  ): Promise<ListRecordsResponse> {
    const params = new URLSearchParams({
      page_size: pageSize.toString()
    })

    if (pageToken) {
      params.append('page_token', pageToken)
    }

    const endpoint = `/bitable/v1/apps/${this.config.appToken}/tables/${this.config.tableId}/records?${params}`
    
    const response = await this.request<ListRecordsResponse>(endpoint)
    return response.data
  }

  /**
   * 获取所有记录（自动处理分页）
   */
  async getAllRecords(): Promise<FeishuRecord[]> {
    const allRecords: FeishuRecord[] = []
    let pageToken: string | undefined
    let hasMore = true

    while (hasMore) {
      const response = await this.listRecords(500, pageToken)
      
      allRecords.push(...response.items)
      
      hasMore = response.has_more
      pageToken = response.page_token
    }

    return allRecords
  }

  /**
   * 获取表格信息
   */
  async getTableInfo(): Promise<any> {
    const endpoint = `/bitable/v1/apps/${this.config.appToken}/tables/${this.config.tableId}`
    const response = await this.request(endpoint)
    return response.data
  }

  /**
   * 获取字段列表
   */
  async getFields(): Promise<any[]> {
    const endpoint = `/bitable/v1/apps/${this.config.appToken}/tables/${this.config.tableId}/fields`
    const response = await this.request(endpoint)
    return response.data.items || []
  }
}

/**
 * 创建飞书API客户端实例
 */
export function createFeishuClient(config: FeishuConfig): FeishuApiClient {
  return new FeishuApiClient(config)
}

/**
 * 验证配置是否完整
 */
export function validateConfig(config: Partial<FeishuConfig>): config is FeishuConfig {
  return !!(config.appToken && config.tableId && config.appId && config.appSecret)
}

/**
 * 从localStorage加载配置
 */
export function loadConfigFromStorage(): FeishuConfig | null {
  try {
    const saved = localStorage.getItem('feishu-config')
    if (saved) {
      const config = JSON.parse(saved)
      if (validateConfig(config)) {
        return config
      }
    }
  } catch (error) {
    console.error('加载飞书配置失败:', error)
  }
  return null
}

/**
 * 保存配置到localStorage
 */
export function saveConfigToStorage(config: FeishuConfig): void {
  try {
    localStorage.setItem('feishu-config', JSON.stringify(config))
  } catch (error) {
    console.error('保存飞书配置失败:', error)
    throw error
  }
}

/**
 * 字段映射配置
 */
export interface FieldMapping {
  [localField: string]: string // 本地字段名 -> 飞书字段名
}

/**
 * 默认字段映射
 */
export const defaultFieldMapping: FieldMapping = {
  updateTime: '更新时间',
  company: '公司',
  applicationLink: '投递链接',
  industry: '行业',
  tags: '标签',
  batch: '批次',
  isHot: '热门',
  position: '职位',
  location: '地点',
  deadline: '投递截至'
}

/**
 * 从localStorage加载字段映射
 */
export function loadFieldMappingFromStorage(): FieldMapping {
  try {
    const saved = localStorage.getItem('field-mappings')
    if (saved) {
      return JSON.parse(saved)
    }
  } catch (error) {
    console.error('加载字段映射失败:', error)
  }
  return defaultFieldMapping
}

/**
 * 保存字段映射到localStorage
 */
export function saveFieldMappingToStorage(mapping: FieldMapping): void {
  try {
    localStorage.setItem('field-mappings', JSON.stringify(mapping))
  } catch (error) {
    console.error('保存字段映射失败:', error)
    throw error
  }
}

/**
 * 转换飞书记录为本地格式
 */
export function convertFeishuRecord(
  record: FeishuRecord,
  mapping: FieldMapping,
  index: number = 0
): any {
  const fields = record.fields || {}

  // 调试更新时间字段
  const updateTimeField = mapping.updateTime
  const updateTimeValue = fields[updateTimeField]
  console.log('更新时间字段映射:', updateTimeField)
  console.log('更新时间原始值:', updateTimeValue)
  console.log('更新时间类型:', typeof updateTimeValue)

  // 处理更新时间字段
  let processedUpdateTime = ''
  if (updateTimeValue) {
    if (typeof updateTimeValue === 'number') {
      // 如果是时间戳，转换为日期字符串
      processedUpdateTime = new Date(updateTimeValue).toLocaleDateString('zh-CN')
    } else if (typeof updateTimeValue === 'string') {
      // 如果是字符串，直接使用
      processedUpdateTime = updateTimeValue
    } else if (updateTimeValue && typeof updateTimeValue === 'object') {
      // 如果是对象，可能是飞书的特殊时间格式
      console.log('更新时间对象详情:', JSON.stringify(updateTimeValue))
      processedUpdateTime = updateTimeValue.text || updateTimeValue.value || String(updateTimeValue)
    }
  }

  console.log('处理后的更新时间:', processedUpdateTime)

  return {
    id: Date.now() + index, // 生成唯一ID
    updateTime: processedUpdateTime,
    company: fields[mapping.company] || '',
    applicationLink: fields[mapping.applicationLink] || '',
    industry: fields[mapping.industry] || '',
    tags: fields[mapping.tags] || '',
    batch: fields[mapping.batch] || '',
    isHot: String(fields[mapping.isHot] || ''), // 转换为字符串
    position: fields[mapping.position] || '',
    location: fields[mapping.location] || '',
    deadline: fields[mapping.deadline] || ''
  }
}
