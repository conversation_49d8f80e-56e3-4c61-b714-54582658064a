import { app, BrowserWindow, ipc<PERSON>ain, shell } from "electron";
import { fileURLToPath } from "node:url";
import path from "node:path";
import https from "node:https";
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const userDataPath = path.join(app.getPath("userData"), "vs-re-data");
app.setPath("userData", userDataPath);
process.env.APP_ROOT = path.join(__dirname, "..");
const VITE_DEV_SERVER_URL = process.env["VITE_DEV_SERVER_URL"];
const MAIN_DIST = path.join(process.env.APP_ROOT, "dist-electron");
const RENDERER_DIST = path.join(process.env.APP_ROOT, "dist");
process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, "public") : RENDERER_DIST;
let win;
function createWindow() {
  win = new BrowserWindow({
    width: 1300,
    height: 900,
    resizable: false,
    maximizable: false,
    frame: false,
    // 移除默认窗口栏
    titleBarStyle: "hidden",
    // 隐藏标题栏
    icon: path.join(process.env.VITE_PUBLIC, "electron-vite.svg"),
    webPreferences: {
      preload: path.join(__dirname, "preload.mjs"),
      contextIsolation: true,
      nodeIntegration: false
    }
  });
  win.webContents.on("did-finish-load", () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL);
  } else {
    win.loadFile(path.join(RENDERER_DIST, "index.html"));
  }
}
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
    win = null;
  }
});
app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
ipcMain.handle("window-minimize", () => {
  if (win) {
    win.minimize();
  }
});
ipcMain.handle("window-maximize", () => {
  if (win) {
    if (win.isMaximized()) {
      win.unmaximize();
    } else {
      win.maximize();
    }
  }
});
ipcMain.handle("window-close", () => {
  if (win) {
    win.close();
  }
});
ipcMain.handle("open-external", (_, url) => {
  shell.openExternal(url);
});
ipcMain.handle("feishu-api-request", async (_, options) => {
  return new Promise((resolve, reject) => {
    const url = new URL(options.url);
    const requestOptions = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname + url.search,
      method: options.method,
      headers: {
        "Content-Type": "application/json",
        ...options.headers
      }
    };
    const req = https.request(requestOptions, (res) => {
      let data = "";
      res.on("data", (chunk) => {
        data += chunk;
      });
      res.on("end", () => {
        const statusCode = res.statusCode || 0;
        try {
          const jsonData = JSON.parse(data);
          resolve({
            ok: statusCode >= 200 && statusCode < 300,
            status: statusCode,
            statusText: res.statusMessage || "",
            data: jsonData
          });
        } catch (error) {
          resolve({
            ok: statusCode >= 200 && statusCode < 300,
            status: statusCode,
            statusText: res.statusMessage || "",
            data
          });
        }
      });
    });
    req.on("error", (error) => {
      reject(error);
    });
    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
});
app.whenReady().then(() => {
  console.log("用户数据目录:", app.getPath("userData"));
  console.log("应用数据目录:", app.getPath("appData"));
  createWindow();
});
export {
  MAIN_DIST,
  RENDERER_DIST,
  VITE_DEV_SERVER_URL
};
