{"name": "vs-re", "private": true, "version": "0.2.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build && electron-builder", "preview": "vite preview"}, "dependencies": {"dexie": "^4.0.11", "pinia": "^3.0.3", "tdesign-icons-vue-next": "^0.3.6", "tdesign-vue-next": "^1.15.2", "vue": "^3.4.21"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "electron": "^30.0.1", "electron-builder": "^24.13.3", "typescript": "^5.2.2", "vite": "^5.1.6", "vite-plugin-electron": "^0.28.6", "vite-plugin-electron-renderer": "^0.14.5", "vue-tsc": "^2.0.26"}, "main": "dist-electron/main.js"}