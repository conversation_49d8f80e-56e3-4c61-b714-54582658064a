<template>
  <div class="custom-title-bar">
    <div class="title-bar-content">
      <div class="app-icon">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2L2 7v10c0 5.55 3.84 10 9 11 1.16.21 2.76.21 3.91 0C20.16 27 24 22.55 24 17V7l-10-5z"/>
        </svg>
      </div>
      <div class="app-title">求职鱼</div>
      <div class="window-controls">
        <button class="control-button minimize" @click="minimizeWindow" title="最小化">
          <svg width="12" height="12" viewBox="0 0 12 12">
            <path d="M2 6h8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
          </svg>
        </button>
        <button class="control-button maximize" @click="maximizeWindow" title="最大化">
          <svg width="12" height="12" viewBox="0 0 12 12">
            <rect x="2" y="2" width="8" height="8" stroke="currentColor" stroke-width="1.5" fill="none"/>
          </svg>
        </button>
        <button class="control-button close" @click="closeWindow" title="关闭">
          <svg width="12" height="12" viewBox="0 0 12 12">
            <path d="M3 3l6 6M9 3l-6 6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 窗口控制函数
const minimizeWindow = () => {
  if (window.electronAPI) {
    window.electronAPI.minimize()
  }
}

const maximizeWindow = () => {
  if (window.electronAPI) {
    window.electronAPI.maximize()
  }
}

const closeWindow = () => {
  if (window.electronAPI) {
    window.electronAPI.close()
  }
}
</script>

<style scoped>
.custom-title-bar {
  height: 32px;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  user-select: none;
  -webkit-app-region: drag; /* 允许拖拽窗口 */
  display: flex;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title-bar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 0 12px;
}

.app-icon {
  display: flex;
  align-items: center;
  color: white;
  margin-right: 12px;
  opacity: 0.9;
}

.app-icon svg {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.app-title {
  flex: 1;
  color: white;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  margin: 0 20px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
}

.window-controls {
  display: flex;
  align-items: center;
  -webkit-app-region: no-drag; /* 按钮区域不允许拖拽 */
  gap: 1px;
}

.control-button {
  width: 46px;
  height: 32px;
  border: none;
  background: transparent;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border-radius: 0;
  position: relative;
  opacity: 0.8;
}

.control-button:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.control-button.close:hover {
  background-color: #ef4444;
  color: white;
}

.control-button.minimize:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

.control-button.maximize:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

.control-button svg {
  pointer-events: none;
  transition: transform 0.2s ease;
}

.control-button:hover svg {
  transform: scale(1.1);
}

.control-button:active {
  transform: scale(0.95);
}

/* 移除按钮的焦点边框 */
.control-button:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* 为按钮添加微妙的分隔线 */
.control-button:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 25%;
  bottom: 25%;
  width: 1px;
  background: rgba(255, 255, 255, 0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .app-title {
    font-size: 13px;
    margin: 0 10px;
  }

  .control-button {
    width: 40px;
  }
}
</style>
